# Poređ<PERSON>je fajlova: response.json vs cenovnik.json

## Osnovne informacije

- **response.json**: 1 linija, kompaktni JSON format
- **cenovnik.json**: 98,672 linija, formatiran JSON sa uvlakama

## Struktura podataka

### response.json struktura
```json
{
  "Kcpp_Cena": 21000.0,
  "Kurs_Red_Br": 1,
  "Kurs_Naziv": "Autodijagnostičar",
  "Kurs_Oznaka": "a",
  "Predmet": "Autodijagnostičar",
  "Uzrast": "Odrasli",
  "Nivo": "ODRASLI-nema nivo",
  "Vrsta": "Individualni",
  "Oblast": "Auto struka",
  // ... dodatna polja
}
```

### cenovnik.json struktura
```json
{
  "Redni broj": 1,
  "Naziv": "Autodijagnostičar",
  "Oznaka": "a",
  "Minimalan_broj_polaznika": 1,
  "Maks<PERSON>lni_broj_polaznika": 1,
  "Ukupno_trajanje_kursa": 240,
  "Meseci": 3,
  // ... dodatna polja
}
```

## Ključne razlike u poljima

### 1. Nazivi polja

| response.json | cenovnik.json | Opis |
|---------------|---------------|------|
| `Kurs_Red_Br` | `Redni broj` | Redni broj kursa |
| `Kurs_Naziv` | `Naziv` | Naziv kursa |
| `Kurs_Oznaka` | `Oznaka` | Oznaka kursa |
| `Kcpp_Cena` | ❌ Nema | Cena kursa (samo u response.json) |
| `Kurs_Maksimalan_Broj_Polaznika` | `Maksimalni_broj_polaznika` | Maksimalan broj polaznika |
| `Kurs_Minimalan_Broj_Polaznika` | `Minimalan_broj_polaznika` | Minimalan broj polaznika |
| `Kurs_Trajanje_Kursa` | `Ukupno_trajanje_kursa` | Ukupno trajanje kursa |
| `Kurs_Trajanje_U_Mesecima` | `Meseci` | Trajanje u mesecima |
| `Kurs_Trajanje_Teorijske_Nastave` | `Trajanje_teorijske_nastave` | Trajanje teorijske nastave |
| `Kurs_Trajanje_Prakticne_Nastave` | `Trajanje_prakticne_nastave` | Trajanje praktične nastave |
| `Kurs_Trajanje_Casa_Teorije` | `Trajanje_casa_teorije` | Trajanje časa teorije |
| `Kurs_Trajanje_Casa_Prakticne_Nastave` | `Trajanje_casa_prakse` | Trajanje časa prakse |
| `Kurs_Obuka` | `Kurs/Obuka` | Tip kursa/obuke |
| `Kurs_Aktivan` | `Aktivan` | Status aktivnosti |

### 2. Polja koja postoje samo u response.json

- `Kcpp_Cena` - Cena kursa (numerička vrednost)
- `Kurs_Napomena` - Napomene o kursu
- `Kurs_Trajanje_Prakse` - Trajanje prakse
- `Vk_Naziv` - Naziv vrste kursa
- `Vok_Naziv` - Naziv vrste obuke/kursa
- `Vok_Oznaka` - Oznaka vrste obuke/kursa
- `Vok_Naziv_2`, `Vok_Oznaka_2` - Dodatne kategorije (uglavnom null)
- `Vok_Naziv_3`, `Vok_Oznaka_3` - Dodatne kategorije (uglavnom null)
- `Vok_Naziv_4`, `Vok_Oznaka_4` - Dodatne kategorije (uglavnom null)

### 3. Polja koja postoje samo u cenovnik.json

- `Dinamika_odrzavanja` - Dinamika održavanja (uglavnom prazno)
- `Ed` - Nepoznato polje (uvek 0)
- `Izbor\r` - Nepoznato polje (uvek "\r")

## Razlike u vrednostima

### 1. Format cene
- **response.json**: Ima eksplicitno polje `Kcpp_Cena` sa numeričkim vrednostima (21000.0, 48000.0, itd.)
- **cenovnik.json**: Nema polje za cenu - cena se verovatno kalkuliše dinamički

### 2. Format nivoa
- **response.json**: `"Nivo": "ODRASLI-nema nivo"`, `"napredni"`, `"osnovni"`
- **cenovnik.json**: `"Nivo": "ODRSL"`, `"napredni"`, `"osnovni"`

### 3. Format vrste obuke
- **response.json**: `"Kurs_Obuka": "Obuka"` ili `"Kurs"`
- **cenovnik.json**: `"Kurs/Obuka": "Stručna obuka"` ili `"Kurs"`

## Broj zapisa

### response.json
- Sadrži **više zapisa** po kursu (različite varijante istog kursa)
- Primer: "Upoznajte se sa osnovama etičkog hakovanja" ima 3 varijante:
  - Individualni (70000.0 RSD)
  - Poluindividualni (56000.0 RSD) 
  - Grupni (45000.0 RSD)

### cenovnik.json
- Sadrži **jedan zapis po kursu** (osnovnu varijantu)
- Primer: "Upoznajte se sa osnovama etičkog hakovanja" ima samo jednu varijantu

## Formatiranje

### response.json
- **Kompaktni format**: Ceo JSON je u jednoj liniji
- **Teži za čitanje**: Bez uvlaka i preloma linija
- **Manji fajl**: Zbog kompaktnog formatiranja

### cenovnik.json  
- **Formatiran JSON**: Sa uvlakama i prelomima linija
- **Lakši za čitanje**: Jasno strukturiran
- **Veći fajl**: Zbog formatiranja (98,672 linija)

## Zaključak

**response.json** izgleda kao **kompletniji dataset** koji sadrži:
- ✅ Cene kurseva
- ✅ Više varijanti po kursu (individualni, grupni, online)
- ✅ Dodatne kategorije i napomene
- ✅ Detaljnije informacije o strukturi kurseva

**cenovnik.json** izgleda kao **pojednostavljeni dataset** koji sadrži:
- ❌ Nema cene (kalkulišu se dinamički)
- ❌ Samo osnovne varijante kurseva
- ✅ Lakši za čitanje zbog formatiranja
- ✅ Jednostavnija struktura

**Preporuka**: response.json je bogatiji podacima i trebalo bi da bude glavni izvor podataka, dok je cenovnik.json verovatno izvoz za frontend aplikaciju.
